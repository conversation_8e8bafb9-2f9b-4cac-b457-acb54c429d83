"""
ICU Lipreading API with legacy LipNet, lightweight VSR, and CTC VSR endpoints
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
import os
import cv2
import numpy as np
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="ICU Lipreading API", version="1.0.0")

# Feature flags for VSR implementations
USE_LIGHTWEIGHT = os.getenv("VSR_IMPL", "legacy") == "lightweight"
USE_CTC = os.getenv("VSR_IMPL", "legacy") == "ctc"

# Import legacy components
try:
    import tensorflow as tf
    import pickle
    from tensorflow.keras.models import load_model
    from demographic_utils import augment_features_with_demographics
    
    # Load legacy models
    try:
        lipnet_model = load_model('models/lipnet_embedding_model.h5')
        logger.info("LipNet embedding model loaded successfully")
    except Exception as e:
        logger.error(f"Error loading LipNet embedding model: {e}")
        lipnet_model = None

    try:
        with open('models/icu_classifier.pkl', 'rb') as f:
            icu_classifier = pickle.load(f)
        logger.info("ICU classifier loaded successfully")
    except Exception as e:
        logger.error(f"Error loading ICU classifier: {e}")
        icu_classifier = None
        
    LEGACY_AVAILABLE = True
    
except ImportError as e:
    logger.warning(f"Legacy dependencies not available: {e}")
    LEGACY_AVAILABLE = False
    lipnet_model = None
    icu_classifier = None

# Import lightweight VSR components
try:
    from backend.lightweight_vsr.infer import predict_phrase as predict_lightweight
    LIGHTWEIGHT_AVAILABLE = True
    logger.info("Lightweight VSR module loaded successfully")
except ImportError as e:
    logger.warning(f"Lightweight VSR not available: {e}")
    LIGHTWEIGHT_AVAILABLE = False

# Import CTC VSR components
CTC_AVAILABLE = False
if USE_CTC:
    try:
        from backend.ctc_vsr.api_router import router as ctc_router
        CTC_AVAILABLE = True
        logger.info("CTC VSR module loaded successfully")
    except ImportError as e:
        logger.warning(f"CTC VSR not available: {e}")
        CTC_AVAILABLE = False
        ctc_router = None

# Mount CTC router if available
if CTC_AVAILABLE and USE_CTC:
    app.include_router(ctc_router)
    logger.info("CTC VSR router mounted successfully")

# Import CTC VSR components
CTC_AVAILABLE = False
if USE_CTC:
    try:
        from backend.ctc_vsr.api_router import router as ctc_router
        app.include_router(ctc_router)
        CTC_AVAILABLE = True
        logger.info("CTC VSR module loaded and router mounted successfully")
    except ImportError as e:
        logger.warning(f"CTC VSR not available: {e}")
        CTC_AVAILABLE = False

# Legacy ICU phrases
LEGACY_ICU_PHRASES = [
    "Call the nurse",
    "Help me", 
    "I cant breathe",
    "I feel sick",
    "I feel tired"
]


async def save_upload_to_tmp(file: UploadFile) -> str:
    """Save uploaded file to temporary location"""
    suffix = Path(file.filename).suffix if file.filename else '.mp4'
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
        content = await file.read()
        tmp_file.write(content)
        return tmp_file.name


@app.get('/health')
async def health_check():
    """Health check endpoint"""
    vsr_impl = "ctc" if USE_CTC else ("lightweight" if USE_LIGHTWEIGHT else "legacy")
    status = {
        "status": "healthy",
        "vsr_impl": vsr_impl,
        "legacy_available": LEGACY_AVAILABLE,
        "lightweight_available": LIGHTWEIGHT_AVAILABLE,
        "ctc_available": CTC_AVAILABLE
    }
    return status


@app.post('/predict_v2')
async def predict_v2(file: UploadFile = File(...)):
    """New lightweight VSR prediction endpoint"""
    if not LIGHTWEIGHT_AVAILABLE:
        raise HTTPException(status_code=503, detail="Lightweight VSR not available")

    if not file.filename:
        raise HTTPException(status_code=400, detail="No video file provided")

    tmp_path = None
    try:
        # Save uploaded file
        tmp_path = await save_upload_to_tmp(file)

        # Predict using lightweight VSR
        result = predict_lightweight(tmp_path)

        return result

    except Exception as e:
        logger.error(f"Error in predict_v2: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up temporary file
        if tmp_path and os.path.exists(tmp_path):
            os.remove(tmp_path)


@app.post('/predict_v3')
async def predict_v3(file: UploadFile = File(...)):
    """CTC VSR prediction endpoint (always uses CTC pipeline)"""
    if not CTC_AVAILABLE:
        raise HTTPException(status_code=503, detail="CTC VSR not available")

    # Forward to CTC router's predict endpoint
    from backend.ctc_vsr.infer import CTCInference

    if not file.filename:
        raise HTTPException(status_code=400, detail="No video file provided")

    tmp_path = None
    try:
        # Save uploaded file
        tmp_path = await save_upload_to_tmp(file)

        # Initialize CTC inference (will use environment variables for model paths)
        inference = CTCInference()

        # Predict using best mode selection
        result = inference.predict_best(tmp_path)

        # Add metadata
        result.update({
            "filename": file.filename,
            "endpoint": "predict_v3"
        })

        return result

    except Exception as e:
        logger.error(f"Error in predict_v3: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up temporary file
        if tmp_path and os.path.exists(tmp_path):
            os.remove(tmp_path)


@app.post('/predict')
async def predict(
    video: UploadFile = File(None, alias="video"),
    file: UploadFile = File(None),
    demographic: str = Form("male_under_50")
):
    """Legacy prediction endpoint with routing to different implementations"""
    # Determine which file parameter was used
    video_file = video if video else file

    if not video_file:
        raise HTTPException(status_code=400, detail="No video file provided")

    if not video_file.filename:
        raise HTTPException(status_code=400, detail="Empty video file")

    # Route based on VSR implementation
    if USE_CTC and CTC_AVAILABLE:
        # Route to CTC implementation
        return await predict_v3(video_file)
    elif USE_LIGHTWEIGHT and LIGHTWEIGHT_AVAILABLE:
        # Route to lightweight implementation
        return await predict_v2(video_file)

    # Use legacy implementation
    if not LEGACY_AVAILABLE:
        raise HTTPException(status_code=503, detail="Legacy implementation not available")

    tmp_path = None
    try:
        # Save uploaded file
        tmp_path = await save_upload_to_tmp(video_file)

        # Extract frames from the video
        frames = extract_frames(tmp_path)

        # Get LipNet embeddings
        embeddings = get_lipnet_embeddings(frames)

        # Augment embeddings with demographic features
        augmented_embeddings = augment_features_with_demographics(embeddings, tmp_path, demographic)

        # Make prediction using the classifier
        prediction_idx, confidence = predict_phrase_legacy(augmented_embeddings)

        return {
            "phrase": LEGACY_ICU_PHRASES[prediction_idx],
            "confidence": float(confidence)
        }

    except Exception as e:
        logger.error(f"Error processing video: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up temporary file
        if tmp_path and os.path.exists(tmp_path):
            os.remove(tmp_path)


# Legacy helper functions
def extract_frames(video_path, max_frames=75):
    """Extract frames from a video file (legacy)"""
    frames = []
    cap = cv2.VideoCapture(video_path)
    
    while len(frames) < max_frames and cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame (grayscale, resize to match model input)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Resize to match LipNet input dimensions (46x140)
        resized = cv2.resize(gray, (140, 46))
        normalized = resized / 255.0  # Normalize pixel values
        
        frames.append(normalized)
    
    cap.release()
    
    # Pad if needed to reach max_frames
    if len(frames) < max_frames:
        last_frame = frames[-1] if frames else np.zeros((46, 140))
        frames.extend([last_frame] * (max_frames - len(frames)))
    
    return np.array(frames)


def get_lipnet_embeddings(frames):
    """Get embeddings from the LipNet model (legacy)"""
    if lipnet_model is None:
        raise ValueError("LipNet embedding model not loaded")
    
    # Reshape for model input (add batch dimension and channel dimension)
    input_frames = frames.reshape(1, frames.shape[0], frames.shape[1], frames.shape[2], 1)
    
    # Get embeddings from the model
    embeddings = lipnet_model.predict(input_frames)
    
    # Flatten the embeddings to a 1D vector by taking the mean across the time dimension
    flattened_embeddings = np.mean(embeddings, axis=1).flatten()
    
    return flattened_embeddings


def predict_phrase_legacy(embeddings):
    """Predict the ICU phrase using the legacy classifier"""
    if icu_classifier is None:
        raise ValueError("ICU classifier not loaded")
    
    # Get prediction probabilities
    try:
        probs = icu_classifier.predict_proba([embeddings])[0]
    except AttributeError:
        logger.warning("Using legacy classifier format")
        probs = icu_classifier.predict_proba([embeddings])[0]
    
    # Get the index of the highest probability
    prediction_idx = np.argmax(probs)
    confidence = probs[prediction_idx]
    
    # Add confidence threshold for more reliable predictions
    if confidence < 0.4:
        logger.warning(f"Low confidence prediction: {confidence:.2f}")
    
    return prediction_idx, confidence


@app.get('/status')
async def status():
    """Detailed status endpoint"""
    vsr_impl = "ctc" if USE_CTC else ("lightweight" if USE_LIGHTWEIGHT else "legacy")

    endpoints = {
        "/predict": f"legacy (or routed to {vsr_impl} if VSR_IMPL={vsr_impl})",
        "/predict_v2": "lightweight VSR",
        "/health": "health check",
        "/status": "detailed status"
    }

    if CTC_AVAILABLE:
        endpoints.update({
            "/predict_v3": "CTC VSR (always uses CTC pipeline)",
            "/ctc/predict": "CTC VSR with automatic mode selection",
            "/ctc/predict_icu": "CTC VSR ICU mode only",
            "/ctc/predict_open": "CTC VSR open mode only",
            "/ctc/health": "CTC VSR health check",
            "/ctc/info": "CTC VSR model information"
        })

    return {
        "vsr_implementation": vsr_impl,
        "legacy_available": LEGACY_AVAILABLE,
        "lightweight_available": LIGHTWEIGHT_AVAILABLE,
        "ctc_available": CTC_AVAILABLE,
        "legacy_models": {
            "lipnet_loaded": lipnet_model is not None,
            "classifier_loaded": icu_classifier is not None
        } if LEGACY_AVAILABLE else None,
        "endpoints": endpoints
    }


if __name__ == '__main__':
    import uvicorn

    vsr_impl = os.getenv('VSR_IMPL', 'legacy')
    logger.info(f"Starting API with VSR_IMPL={vsr_impl}")
    logger.info(f"Legacy available: {LEGACY_AVAILABLE}")
    logger.info(f"Lightweight available: {LIGHTWEIGHT_AVAILABLE}")
    logger.info(f"CTC available: {CTC_AVAILABLE}")

    uvicorn.run(app, host='0.0.0.0', port=8000)
